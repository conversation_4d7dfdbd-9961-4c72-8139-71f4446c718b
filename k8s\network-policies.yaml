apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: whatsapp-hotel-bot-network-policy
  namespace: hotel-bot
spec:
  podSelector:
    matchLabels:
      app: whatsapp-hotel-bot
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow ingress from ingress controller
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 8000
  # Allow ingress from monitoring
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 8000
  # Allow ingress within namespace
  - from:
    - namespaceSelector:
        matchLabels:
          name: hotel-bot
    ports:
    - protocol: TCP
      port: 8000
  egress:
  # Allow egress to database
  - to:
    - podSelector:
        matchLabels:
          app: postgres
    ports:
    - protocol: TCP
      port: 5432
  # Allow egress to Redis
  - to:
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379
  # Allow egress to external APIs (Green API, DeepSeek)
  - to: []
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
  # Allow DNS resolution
  - to:
    - namespaceSelector:
        matchLabels:
          name: kube-system
    ports:
    - protocol: UDP
      port: 53

---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: postgres-network-policy
  namespace: hotel-bot
spec:
  podSelector:
    matchLabels:
      app: postgres
  policyTypes:
  - Ingress
  ingress:
  # Allow ingress from application pods
  - from:
    - podSelector:
        matchLabels:
          app: whatsapp-hotel-bot
    ports:
    - protocol: TCP
      port: 5432
  # Allow ingress from backup jobs
  - from:
    - podSelector:
        matchLabels:
          component: backup
    ports:
    - protocol: TCP
      port: 5432

---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: redis-network-policy
  namespace: hotel-bot
spec:
  podSelector:
    matchLabels:
      app: redis
  policyTypes:
  - Ingress
  ingress:
  # Allow ingress from application pods
  - from:
    - podSelector:
        matchLabels:
          app: whatsapp-hotel-bot
    ports:
    - protocol: TCP
      port: 6379

---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: monitoring-network-policy
  namespace: hotel-bot
spec:
  podSelector:
    matchLabels:
      component: monitoring
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow ingress from ingress controller
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 9090
    - protocol: TCP
      port: 3000
  # Allow ingress from monitoring namespace
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
  egress:
  # Allow egress to scrape metrics from application pods
  - to:
    - podSelector:
        matchLabels:
          app: whatsapp-hotel-bot
    ports:
    - protocol: TCP
      port: 8000
  # Allow egress to Kubernetes API
  - to: []
    ports:
    - protocol: TCP
      port: 443
  # Allow DNS resolution
  - to:
    - namespaceSelector:
        matchLabels:
          name: kube-system
    ports:
    - protocol: UDP
      port: 53

---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: default-deny-all
  namespace: hotel-bot
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress

---
# Staging environment network policies
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: whatsapp-hotel-bot-network-policy
  namespace: hotel-bot-staging
spec:
  podSelector:
    matchLabels:
      app: whatsapp-hotel-bot
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow ingress from ingress controller
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 8000
  # Allow ingress within namespace
  - from:
    - namespaceSelector:
        matchLabels:
          name: hotel-bot-staging
    ports:
    - protocol: TCP
      port: 8000
  egress:
  # Allow egress to database
  - to:
    - podSelector:
        matchLabels:
          app: postgres
    ports:
    - protocol: TCP
      port: 5432
  # Allow egress to Redis
  - to:
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379
  # Allow egress to external APIs
  - to: []
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
  # Allow DNS resolution
  - to:
    - namespaceSelector:
        matchLabels:
          name: kube-system
    ports:
    - protocol: UDP
      port: 53
